"""
好氧池运行参数计算模块

本模块提供了计算污水处理厂好氧池运行参数的功能，主要包括:
1. 计算污泥负荷(F/M比)
2. 确定目标MLSS浓度
3. 计算排泥需求
4. 生成运行建议

主要类:
- AerobicDataCollector: 负责从摄像头获取今天和昨天的运行数据
- AerobicTankAnalyzer: 负责根据收集的数据进行分析和生成运行建议

典型使用场景:
1. 日常运行监控
2. 排泥计划制定
3. MLSS浓度调整决策
"""

import sys
import os
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import math
import logging
import datetime
import json
from json_repair import loads
from server.utils.data_publisher import DataPublisher
# from server.utils.logger import setup_logging
from server.utils.sensor_manager import SensorManager
from config_file import config
from server.utils.logger import setup_logging
from server.utils.get_cameras_info import get_cameras_config
from server.remote.point import APIClient
from llms.llm_api_server import llm_process_sludge_discharge
from pathlib import Path

base_dataset_path = config.env['storage']['paths']['base_dataset']
# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

# 初始化 API 客户端
api_client = APIClient()

class AerobicDataCollector:
    """
    好氧池数据收集类
    负责从摄像头获取今天和昨天的运行数据
    """
    def __init__(self, api_client):
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.cameras_config = get_cameras_config()
        self.aerobic_camera_ids = self._get_aerobic_camera_ids()
        self.aerobic_data_by_camera = {}
        self.today_point_codes = ["污水处理量", "BOD", "mlss", "出水BOD"]
        self.yesterday_point_codes = ["Qy", "Sy_BOD", "Xy_MLSS", "S2_BOD"]
        self.today_to_yesterday_mapping = {
            "污水处理量": "Qy",
            "BOD": "Sy_BOD",
            "mlss": "Xy_MLSS",
            "出水BOD": "S2_BOD"
        }
        self.sensor_manager = SensorManager(config.env)
    def _get_aerobic_camera_ids(self):
        """获取好氧池相关的摄像头ID列表"""
        aerobic_ids = []
        for camera_info in self.cameras_config:
            if "aerobic" in camera_info.get("system_type", ""):
                camera_id = camera_info.get("camera_id")
                if camera_id:
                    aerobic_ids.append(camera_id)
                    self.logger.info(f"Found aerobic system camera: {camera_id} in {camera_info.get('video_id', 'N/A')}")
                else:
                    self.logger.warning(f"Found aerobic system type but no camera_id in: {camera_info}")
        return aerobic_ids
    
    def collect_data(self):
        """收集所有好氧池摄像头的今天和昨天的数据"""
        self._setup_time_ranges()
        
        for camera_id in self.aerobic_camera_ids:
            try:
                self._collect_camera_data(camera_id)
            except Exception as e:
                self.logger.error(f"处理相机 ID 时出错 {camera_id}: {e}", exc_info=True)
                
        self.logger.info(f"收集到的好氧池数据: {self.aerobic_data_by_camera}")
        return self.aerobic_data_by_camera
    
    def _setup_time_ranges(self):
        """设置今天和昨天的时间范围"""
        now = datetime.datetime.now()
        # 今天的时间范围
        self.today_end_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.today_start_time = now.strftime("%Y-%m-%d 00:00:00")
        
        # 昨天的时间范围
        yesterday = now - datetime.timedelta(days=1)
        self.yesterday_start_time = yesterday.strftime("%Y-%m-%d 00:00:00")
        self.yesterday_end_time = yesterday.strftime("%Y-%m-%d 23:59:59")
        
        self.logger.info(f"查询今天数据的时间范围：从 {self.today_start_time} 到 {self.today_end_time}")
        self.logger.info(f"查询昨天数据的时间范围：从 {self.yesterday_start_time} 到 {self.yesterday_end_time}")
    
    def _collect_camera_data(self, camera_id):
        """收集单个摄像头的数据"""
        self.logger.info(f"Fetching points for aerobic camera_id: {camera_id}")
        device_points = self.api_client.get_device_points(
            device_id=camera_id,
            bind_status="1",
            query_value="true"
        )
        self.logger.info(f"Received points for camera_id {camera_id}: {device_points}")
        
        # 确保返回的是列表
        if not isinstance(device_points.get('data', []), list):
            self.logger.warning(f"Expected a list of points for camera_id {camera_id}, but received: {type(device_points)}. Skipping.")
            return
            
        # 初始化此摄像头的数据字典
        camera_data = {
            "camera_id": camera_id,
            "today_parameters": {},
            "yesterday_parameters": {}
        }
        
        # 遍历获取到的设备点信息
        for point_info in device_points.get('data', []):
            self._process_point(point_info, camera_data)
        
        # 将此摄像头的数据添加到总字典中
        if camera_data["today_parameters"] or camera_data["yesterday_parameters"]:  # 只添加有参数数据的摄像头
            self.aerobic_data_by_camera[camera_id] = camera_data
            
    def _process_point(self, point_info, camera_data):
        """处理单个点位的数据"""
        point_code = point_info.get("point_code")
        point_id = point_info.get("point_id")
        
        # 只关注我们需要的 point_code（今天的数据）
        if point_code in self.today_point_codes and point_id:
            self.logger.info(f"Found target point_code: {point_code} with point_id: {point_id} from camera_id: {camera_data['camera_id']}")
            
            # 获取今天的数据
            self._get_today_data(point_id, point_code, camera_data)
            
            # 获取昨天的数据
            self._get_yesterday_data(point_id, point_code, camera_data)
    
    def _get_today_data(self, point_id, point_code, camera_data):
        """获取今天的数据"""
        try:
            # 获取传感器数据
            sensor_data = self.sensor_manager.get_sensor_data_all(camera_data["camera_id"])
            
            if sensor_data:
                self.logger.info(f"从sensor_manager获取到数据: {sensor_data}")
                
                # 根据point_code直接从sensor_data中获取对应数据
                if point_code == "污水处理量" and "污水处理量" in sensor_data:
                    today_value = sensor_data["污水处理量"]
                elif point_code == "BOD" and "BOD" in sensor_data:
                    today_value = sensor_data["BOD"]
                elif point_code == "mlss" and "MLSS" in sensor_data:  # 注意这里使用的是sensor_data中的MLSS键
                    today_value = sensor_data["MLSS"]
                elif point_code == "出水BOD" and "出水BOD" in sensor_data:
                    today_value = sensor_data["出水BOD"]
                else:
                    self.logger.warning(f"在sensor_data中未找到对应的 {point_code} 数据")
                    return
                
                # 获取当前时间作为monitor_time
                today_monitor_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                # 存储今天的数据
                camera_data["today_parameters"][point_code] = {
                    "point_id": point_id,
                    "value": today_value,
                    "monitor_time": today_monitor_time
                }
                
                self.logger.info(f"成功从sensor_data获取今天的 {point_code} 值: {today_value} 在 {today_monitor_time}")
            else:
                self.logger.warning(f"未能从sensor_manager获取到有效数据")
                
        except Exception as e:
            self.logger.error(f"获取今天的数据时出错 point_id {point_id}: {e}", exc_info=True)
    
    def _get_yesterday_data(self, point_id, point_code, camera_data):
        """获取昨天的数据"""
        try:
            yesterday_history_data = self.api_client.get_point_history(
                point_id=point_id,
                start_time=self.yesterday_start_time,
                end_time=self.yesterday_end_time,
                time_type="day",
                page_size=10,
                page_num=1
            )
            
            # 找到对应的昨天的变量名
            yesterday_point_code = self.today_to_yesterday_mapping.get(point_code)
            
            if isinstance(yesterday_history_data, dict) and yesterday_history_data.get("code") == 200:
                yesterday_rows = yesterday_history_data.get("data", {}).get("rows", [])
                if yesterday_rows and len(yesterday_rows) > 0:
                    # 按照 monitor_time 降序排序，取最新的一条
                    sorted_yesterday_rows = sorted(yesterday_rows, key=lambda x: x.get("monitor_time", ""), reverse=True)
                    latest_yesterday_data = sorted_yesterday_rows[0]
                    yesterday_value = latest_yesterday_data.get("value")
                    yesterday_monitor_time = latest_yesterday_data.get("monitor_time")
                    
                    # 存储昨天的数据（使用对应的变量名）
                    camera_data["yesterday_parameters"][yesterday_point_code] = {
                        "point_id": point_id,
                        "value": yesterday_value,
                        "monitor_time": yesterday_monitor_time
                    }
                    
                    self.logger.info(f"Successfully retrieved latest yesterday's {yesterday_point_code} value: {yesterday_value} at {yesterday_monitor_time} (from {len(yesterday_rows)} records)")
                else:
                    self.logger.warning(f"No yesterday's history data rows found for point_id: {point_id}")
            else:
                self.logger.warning(f"Failed to get yesterday's history data for point_id: {point_id}, response: {yesterday_history_data}")
                
        except Exception as yesterday_hist_err:
            self.logger.error(f"Error getting yesterday's history data for point_id {point_id}: {yesterday_hist_err}", exc_info=True)
class AerobicTankAnalyzer:
    """
    好氧池运行分析类
    负责根据收集的数据进行分析和生成运行建议
    """
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def convert_simple_data_format(self, simple_data):
        """
        将简单的用户数据格式转换为内部数据格式

        Args:
            simple_data (dict): 简单格式的用户数据，包含：
                - tank_volume: 好氧池有效体积 (m³)
                - yesterday_flow: 昨天处理水量 (m³/d)
                - today_mlss: 今天MLSS浓度 (mg/L)
                - today_influent_bod: 今天进水BOD浓度 (mg/L)
                - today_effluent_bod: 今天出水BOD浓度 (mg/L)
                - today_tn: 今天进水总氮浓度 (mg/L) [可选]
                - today_do: 今天DO浓度 (mg/L) [可选]
                - pump_flow_rate: 单台排泥泵流量 (m³/h)
                - pump_count: 可用排泥泵数量

        Returns:
            dict: 内部格式的数据结构
        """
        from datetime import datetime

        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        yesterday_time = datetime.now().strftime("%Y-%m-%d 00:00:00")

        # 对于缺少的昨天数据，使用合理的假设
        # 假设昨天的BOD浓度与今天相近，MLSS比今天低100-200
        yesterday_influent_bod = simple_data.get('yesterday_influent_bod', simple_data['today_influent_bod'] * 0.95)
        yesterday_effluent_bod = simple_data.get('yesterday_effluent_bod', simple_data['today_effluent_bod'] * 0.9)
        yesterday_mlss = simple_data.get('yesterday_mlss', simple_data['today_mlss'] - 150)

        # 构建内部数据格式
        internal_data = {
            "camera_user_input": {
                "camera_id": "user_input",
                "today_parameters": {
                    "污水处理量": {
                        "point_id": "user_flow",
                        "value": simple_data['yesterday_flow'],  # 使用昨天的处理量作为今天的处理量
                        "monitor_time": current_time
                    },
                    "BOD": {
                        "point_id": "user_bod",
                        "value": simple_data['today_influent_bod'],
                        "monitor_time": current_time
                    },
                    "mlss": {
                        "point_id": "user_mlss",
                        "value": simple_data['today_mlss'],
                        "monitor_time": current_time
                    },
                    "出水BOD": {
                        "point_id": "user_effluent_bod",
                        "value": simple_data['today_effluent_bod'],
                        "monitor_time": current_time
                    }
                },
                "yesterday_parameters": {
                    "Qy": {
                        "point_id": "user_flow",
                        "value": simple_data['yesterday_flow'],
                        "monitor_time": yesterday_time
                    },
                    "Sy_BOD": {
                        "point_id": "user_bod",
                        "value": yesterday_influent_bod,
                        "monitor_time": yesterday_time
                    },
                    "Xy_MLSS": {
                        "point_id": "user_mlss",
                        "value": yesterday_mlss,
                        "monitor_time": yesterday_time
                    },
                    "S2_BOD": {
                        "point_id": "user_effluent_bod",
                        "value": yesterday_effluent_bod,
                        "monitor_time": yesterday_time
                    }
                }
            }
        }

        return internal_data

    def analyze_simple_data_format(self, simple_data):
        """
        直接分析简单格式的用户数据

        Args:
            simple_data (dict): 简单格式的用户数据

        Returns:
            dict: 分析结果
        """
        # 转换数据格式
        internal_data = self.convert_simple_data_format(simple_data)

        # 使用现有的分析方法
        analysis_results = self.analyze_collected_data(
            internal_data,
            simple_data['tank_volume'],
            simple_data['pump_flow_rate'],
            simple_data['pump_count']
        )

        return analysis_results
    
    def calculate_aerobic_tank_operation(
        self,
        # --- Design Parameters ---
        V: float,              # 好氧池有效体积 (m³) - Effective volume of the aerobic tank
        R_external: float,     # 外回流比 (如 0.5 表示 50%) - External recirculation ratio (e.g., 0.5 for 50%)

        # --- Yesterday's Data ---
        Qy: float,             # 上一天处理水量 (m³/day) - Yesterday's flow rate
        Sy_BOD: float,         # 上一天进水BOD浓度 (mg/L or g/m³) - Yesterday's influent BOD concentration
        Xy_MLSS: float,        # 上一天好氧池MLSS浓度 (mg/L or g/m³) - Yesterday's MLSS concentration

        # --- Today's Data ---
        Qt: float,             # 今天当前处理水量 (m³/day) - Today's current flow rate (不再使用于计算)
        St_BOD: float,         # 今天当前进水BOD浓度 (mg/L or g/m³) - Today's current influent BOD concentration
        St_BOD_effluent: float, # 今天当前出水BOD浓度 (mg/L or g/m³) - Today's current effluent BOD concentration
        Xt_MLSS_actual: float, # 今天当前好氧池MLSS浓度 (mg/L or g/m³) - Today's current actual MLSS concentration
        Qw: float,             # 单台排泥泵流量 (m³/hour) - Flow rate of a single sludge wasting pump
        num_pumps: int,        # 计划用于排泥的泵数量 - Number of pumps planned for wasting
        TNt: float = None,     # 今天当前出水总氮 (mg/L) - Optional: Today's effluent Total Nitrogen
        # --- Other potentially relevant data for analysis (not used in core calc) ---
        # DOt: float = None,         # 今天DO浓度 (mg/L) - Today's Dissolved Oxygen
        # SV30t: float = None,       # 今天SV30 (%) - Today's Sludge Volume Index 30 min
        # SRTt: float = None         # 今天实际SRT (days) - Today's actual Sludge Retention Time
    ) -> dict:
        """
        根据提供的污水处理厂好氧池运行数据，使用专业工艺计算方式计算排泥需求和时间。

        计算步骤：
        1. 计算污泥增量：Vincrease = Y*(S1-S2)*Qy，其中Y=1.25（产泥系数）
        2. 使用固定目标污泥负荷：F/M = 0.111 kgBOD/(kgMLSS·d)
        3. 计算目标MLSS浓度：Xtarget = Qy*S1/V/(F/M)
        4. 计算需要排出的污泥量：!X = (Xactual-Xtarget)*V
        5. 计算所需排泥干重：Xp = !X + Vincrease
        6. 计算排泥时间：T = Xp/(MLSSv*Qw)，其中MLSSv = 2*Xactual

        Args:
            V: 好氧池有效体积 (m³)
            R_external: 外回流比 (decimal, e.g., 0.5) - 保留参数但不用于计算
            Qy: 上一天处理水量 (m³/day)
            Sy_BOD: 上一天进水BOD浓度 (mg/L or g/m³)
            Xy_MLSS: 上一天好氧池MLSS浓度 (mg/L or g/m³)
            Qt: 今天当前处理水量 (m³/day) - 不用于计算
            St_BOD: 今天当前进水BOD浓度 (mg/L or g/m³)
            St_BOD_effluent: 今天当前出水BOD浓度 (mg/L or g/m³)
            Xt_MLSS_actual: 今天当前好氧池MLSS浓度 (mg/L or g/m³)
            Qw: 单台排泥泵流量 (m³/hour)
            num_pumps: 计划用于排泥的泵数量
            TNt: (Optional) 今天当前出水总氮 (mg/L)

        Returns:
            包含计算结果和分析建议的字典：
            - 'F_M_target': 目标污泥负荷 (固定值0.111)
            - 'X_target': 目标MLSS浓度 (mg/L)
            - 'Vincrease': 污泥增量 (kg)
            - 'delta_X_mass': 需要排出的污泥总量 (kg)
            - 'Xp': 所需排泥干重 (kg)
            - 'wasting_needed': 是否需要排泥 (bool)
            - 'MLSSv': 回流污泥浓度 (mg/L)
            - 'wasting_time_hours': 排泥时间 (hours)
            - 'analysis': 分析过程 (str)
            - 'recommendation': 操作建议 (str)
        """
        results = {
            'F_M_target': 0.111,  # 固定目标污泥负荷
            'X_target': None,
            'Vincrease': None,  # 污泥增量
            'delta_X_mass': None,
            'Xp': None,  # 所需排泥干重
            'wasting_needed': False,
            'MLSSv': None,  # 回流污泥浓度
            'total_Qw': None,
            'wasting_time_hours': 0.0,
            'analysis': [],
            'recommendation': ""
        }

        # --- Step 1: 计算污泥增量 Vincrease = Y*(S1-S2)*Qy ---
        if V <= 0:
            results['analysis'].append("错误：好氧池体积(V)不能为零或负数。无法计算。")
            results['recommendation'] = "请检查输入的设计参数。"
            return results

        try:
            # 污泥增量计算，Y=1.25为产泥系数
            # 自动识别Qy的单位：如果Qy > 1000，认为是m³/day；否则认为是万m³/day
            Y = 1.25  # 产泥系数

            if Qy > 1000:
                # 直接使用m³/day单位
                Qy_m3_per_day = Qy
                unit_display = f"{Qy:.0f} m³/d"
            else:
                # 将万m³/day转换为m³/day
                Qy_m3_per_day = Qy * 10000
                unit_display = f"{Qy:.2f}万m³/d × 10000"

            Vincrease = Y * (St_BOD - St_BOD_effluent) * Qy_m3_per_day / 1000  # 转换为kg
            results['Vincrease'] = Vincrease
            results['analysis'].append(f"1. 计算污泥增量：Vincrease = {Y} × ({St_BOD:.2f} - {St_BOD_effluent:.2f}) × {unit_display} / 1000 = {Vincrease:.2f} kg")

            # 设定固定的目标污泥负荷
            results['analysis'].append(f"2. 使用设计目标污泥负荷：F/M = {results['F_M_target']:.3f} kgBOD/(kgMLSS·d)")

        except Exception as e:
            results['analysis'].append(f"错误：计算污泥增量时发生错误: {e}")
            results['recommendation'] = "请检查输入的BOD数据和处理水量。"
            return results

        # --- Step 2: 计算目标MLSS浓度 Xtarget = Qy*S1/V/(F/M) ---
        try:
            # 使用昨天的处理水量和今天的进水BOD浓度计算目标MLSS
            # 自动识别Qy的单位：如果Qy > 1000，认为是m³/day；否则认为是万m³/day
            if Qy > 1000:
                # 直接使用m³/day单位
                Qy_m3_per_day = Qy
                unit_display = f"{Qy:.0f} m³/d"
            else:
                # 将万m³/day转换为m³/day
                Qy_m3_per_day = Qy * 10000
                unit_display = f"{Qy:.2f}万m³/d × 10000"

            X_target = (Qy_m3_per_day * St_BOD) / (V * results['F_M_target'])
            results['X_target'] = X_target
            results['analysis'].append(f"3. 计算目标MLSS浓度：Xtarget = {unit_display} × {St_BOD:.2f} / ({V:.2f} × {results['F_M_target']:.3f}) = {X_target:.2f} mg/L")
        except ZeroDivisionError:
            results['analysis'].append("错误：计算目标MLSS时发生除零错误（检查V和F/M_target）。")
            results['recommendation'] = "池体积或目标污泥负荷不能为零。"
            return results
        except Exception as e:
            results['analysis'].append(f"错误：计算目标MLSS时发生错误: {e}")
            results['recommendation'] = "请检查输入数据（Qy, St_BOD, V）。"
            return results


        # --- Step 3: 计算需要排出的污泥总量 !X = (Xactual-Xtarget)*V ---
        # 单位转换：(mg/L - mg/L) × m³ / 1000 = kg
        # mg/L × m³ = mg × 1000L/L = mg × 1000 = g，再除以1000转换为kg
        delta_X_mass = (Xt_MLSS_actual - results['X_target']) * V / 1000
        results['delta_X_mass'] = delta_X_mass

        results['analysis'].append(f"4. 当前实际MLSS浓度：{Xt_MLSS_actual:.2f} mg/L")
        results['analysis'].append(f"5. 计算需要排出的污泥总量：!X = ({Xt_MLSS_actual:.2f} - {results['X_target']:.2f}) × {V:.2f} / 1000 = {delta_X_mass:.2f} kg")

        # --- Step 4: 计算所需排泥干重 Xp = !X + Vincrease ---
        Xp = delta_X_mass + results['Vincrease']
        results['Xp'] = Xp
        results['analysis'].append(f"6. 计算所需排泥干重：Xp = {delta_X_mass:.2f} + {results['Vincrease']:.2f} = {Xp:.2f} kg")

        # --- Step 5: 判断是否需要排泥 ---
        if Xp <= 0:
            results['wasting_needed'] = False
            results['analysis'].append("7. 判断结果：Xp ≤ 0，不需要进行排泥")

            # --- 专业评估无需排泥的情况 ---
            results['analysis'].append(f"8. 专业工艺评估：")
            mlss_difference = Xt_MLSS_actual - results['X_target']

            if delta_X_mass <= 0:
                results['analysis'].append(f"    - 当前MLSS({Xt_MLSS_actual:.0f}) 低于目标MLSS({results['X_target']:.0f})")
                results['analysis'].append(f"    - MLSS差值：{abs(mlss_difference):.0f} mg/L")

                if Xt_MLSS_actual < results['X_target'] * 0.9:
                    results['analysis'].append(f"    - 警告：MLSS显著偏低，需要提高MLSS浓度")
                else:
                    results['analysis'].append(f"    - MLSS略低于目标值，属于正常波动范围")
            else:
                results['analysis'].append(f"    - 虽然MLSS略高，但污泥增量足以平衡，无需排泥")

            # --- 制定提高MLSS的具体建议 ---
            if delta_X_mass <= 0:
                recommendation = f"=== MLSS浓度管理建议 ===\n"
                recommendation += f"当前状态：MLSS浓度 {Xt_MLSS_actual:.0f} mg/L，目标浓度 {results['X_target']:.0f} mg/L\n\n"

                if Xt_MLSS_actual < results['X_target'] * 0.9:
                    recommendation += f"=== 提高MLSS的操作措施 ===\n"
                    recommendation += f"1. 立即停止排泥操作\n"
                    recommendation += f"2. 检查系统是否存在污泥流失：\n"
                    recommendation += f"   • 检查二沉池刮泥机运行状况\n"
                    recommendation += f"   • 确认回流污泥泵正常工作\n"
                    recommendation += f"   • 检查管道是否有泄漏\n"
                    recommendation += f"3. 调整运行参数：\n"
                    recommendation += f"   • 适当降低外回流比至50-70%\n"
                    recommendation += f"   • 确保内回流比在100-150%范围\n"
                    recommendation += f"   • 控制曝气量，避免过度曝气导致污泥解体\n"
                    recommendation += f"4. 监测措施：\n"
                    recommendation += f"   • 每4小时检测MLSS浓度\n"
                    recommendation += f"   • 观察污泥沉降性能（SV30）\n"
                    recommendation += f"   • 监测出水SS指标\n\n"

                    recommendation += f"=== 预期效果 ===\n"
                    recommendation += f"• 预计3-5天内MLSS可恢复至目标范围\n"
                    recommendation += f"• 如7天内仍无改善，考虑投加种泥或调整工艺参数"
                else:
                    recommendation += f"=== 维持现状建议 ===\n"
                    recommendation += f"• 当前MLSS在可接受范围内，继续正常运行\n"
                    recommendation += f"• 加强日常监测，每日检测MLSS浓度\n"
                    recommendation += f"• 注意观察污泥增长趋势\n"
                    recommendation += f"• 如连续3天MLSS持续下降，及时采取措施"
            else:
                recommendation = f"=== 运行状态良好 ===\n"
                recommendation += f"• 当前系统运行稳定，MLSS浓度在合理范围\n"
                recommendation += f"• 污泥增量({results['Vincrease']:.0f} kg)能够平衡系统需求\n"
                recommendation += f"• 建议维持现有运行参数\n"
                recommendation += f"• 继续按计划进行日常监测"

            results['recommendation'] = recommendation
        else:
            results['wasting_needed'] = True
            results['analysis'].append("7. 判断结果：Xp > 0，需要进行排泥")

            # --- Step 6: 计算回流污泥浓度 MLSSv = 2 * Xactual ---
            MLSSv = 2 * Xt_MLSS_actual
            results['MLSSv'] = MLSSv
            results['analysis'].append(f"8. 计算回流污泥浓度：MLSSv = 2 × {Xt_MLSS_actual:.2f} = {MLSSv:.2f} mg/L")

            # --- Step 7: 计算排泥时间 T = Xp / (MLSSv * Qw) ---
            if Qw <= 0 or num_pumps <= 0:
                results['analysis'].append("错误：排泥泵流量(Qw)或数量(num_pumps)为零或负数。")
                results['recommendation'] = "请提供有效的排泥泵参数。无法计算排泥时间。"
                results['wasting_time_hours'] = None
                return results
            if MLSSv <= 0:
                results['analysis'].append("错误：计算得到的回流污泥浓度MLSSv无效（零或负数）。")
                results['recommendation'] = "无法根据无效的回流污泥浓度计算排泥时间。"
                results['wasting_time_hours'] = None
                return results

            total_Qw = Qw * num_pumps # Total wasting flow rate in m³/hour
            results['total_Qw'] = total_Qw

            try:
                # 使用新的计算公式：T = Xp / MLSSv / Qw
                # 单位转换：Xp(kg) / MLSSv(mg/L) / Qw(m³/h) = hours
                # 需要进行单位转换：Xp(kg) / (MLSSv(mg/L) / 1000) / Qw(m³/h) = Xp(kg) / (MLSSv(g/L)) / Qw(m³/h)
                # = Xp(kg) × 1000(g/kg) / MLSSv(g/L) / Qw(m³/h) = Xp×1000 / MLSSv / Qw (L/h)
                # = Xp×1000 / MLSSv / (Qw×1000) (m³/h) = Xp / MLSSv / Qw (hours)
                wasting_time_hours = Xp / MLSSv / total_Qw * 1000
                results['wasting_time_hours'] = wasting_time_hours

                results['analysis'].append(f"9. 计算排泥时间：T = {Xp:.2f} / {MLSSv:.2f} / {total_Qw:.2f} × 1000 = {wasting_time_hours:.2f} 小时")

                # --- 第三步：重新审视计算准确性 ---
                results['analysis'].append(f"10. 计算准确性审查：")
                results['analysis'].append(f"    - 污泥增量计算：✓ 使用产泥系数1.25，BOD去除量{(St_BOD - St_BOD_effluent):.2f} mg/L")
                results['analysis'].append(f"    - 目标MLSS计算：✓ 使用固定F/M比0.111，处理水量{Qy:.2f}")
                results['analysis'].append(f"    - 排泥量计算：✓ 当前MLSS({Xt_MLSS_actual:.2f}) vs 目标MLSS({results['X_target']:.2f})")
                results['analysis'].append(f"    - 排泥时间计算：✓ 使用回流污泥浓度{MLSSv:.2f} mg/L")

                # --- 第四步：专业工艺评估 ---
                results['analysis'].append(f"11. 专业工艺评估：")

                # 评估排泥时间合理性
                if wasting_time_hours > 72:
                    results['analysis'].append(f"    - 排泥时间({wasting_time_hours:.1f}小时)较长，建议分批进行")
                elif wasting_time_hours < 2:
                    results['analysis'].append(f"    - 排泥时间({wasting_time_hours:.1f}小时)较短，建议精确控制")
                else:
                    results['analysis'].append(f"    - 排泥时间({wasting_time_hours:.1f}小时)在合理范围内")

                # 评估MLSS调整幅度
                mlss_reduction = Xt_MLSS_actual - results['X_target']
                mlss_reduction_percent = (mlss_reduction / Xt_MLSS_actual) * 100
                results['analysis'].append(f"    - MLSS需降低{mlss_reduction:.0f} mg/L ({mlss_reduction_percent:.1f}%)")

                if mlss_reduction_percent > 20:
                    results['analysis'].append(f"    - 注意：MLSS降幅较大，建议分阶段进行，避免冲击负荷")

                # --- 第五步：制定具体操作建议 ---
                recommendation = f"=== 排泥操作建议 ===\n"
                recommendation += f"1. 排泥设备配置：启动 {num_pumps} 台排泥泵（流量{total_Qw} m³/h）\n"
                recommendation += f"2. 排泥时间安排：连续运行 {wasting_time_hours:.1f} 小时\n"
                recommendation += f"3. 目标控制：将MLSS从 {Xt_MLSS_actual:.0f} mg/L 降至 {results['X_target']:.0f} mg/L\n\n"

                recommendation += f"=== 操作注意事项 ===\n"
                recommendation += f"• 排泥前确认排泥泵正常运行\n"
                recommendation += f"• 每2小时检测一次MLSS浓度变化\n"
                recommendation += f"• 监测出水水质指标（BOD、SS、氨氮等）\n"
                recommendation += f"• 如MLSS下降过快，可适当缩短排泥时间\n\n"

                # --- 考虑出水总氮的影响 ---
                if TNt is not None and TNt > 15:
                    recommendation += f"=== 总氮控制建议 ===\n"
                    recommendation += f"• 当前出水总氮 {TNt:.1f} mg/L 偏高\n"
                    adjusted_time = wasting_time_hours * 0.8
                    recommendation += f"• 建议适当减少排泥时间至 {adjusted_time:.1f} 小时\n"
                    recommendation += f"• 保持较高污泥龄以促进硝化反硝化\n"
                    recommendation += f"• 加强内回流控制，提高脱氮效果\n\n"

                recommendation += f"=== 后续监控 ===\n"
                recommendation += f"• 排泥完成后24小时内密切监测MLSS恢复情况\n"
                recommendation += f"• 根据进水负荷变化及时调整运行参数\n"
                recommendation += f"• 建议3-5天后重新评估是否需要进一步排泥"

                results['recommendation'] = recommendation

            except ZeroDivisionError:
                results['analysis'].append("错误：计算排泥时间时发生除零错误（检查total_Qw或MLSSv）。")
                results['recommendation'] = "排泥泵总流量或回流污泥浓度计算为零。无法计算排泥时间。"
                results['wasting_time_hours'] = None
            except Exception as e:
                 results['analysis'].append(f"错误：计算排泥时间时发生错误: {e}")
                 results['recommendation'] = "计算排泥时间时发生错误，请检查所有相关输入数据。"
                 results['wasting_time_hours'] = None


        # Join analysis points into a single string
        results['analysis'] = "\n".join(results['analysis'])

        return results

    def analyze_aerobic_tank_range(
        self,
        V: float, Qy: float, Sy_BOD: float, Xy_MLSS: float,
        Qt: float, St_BOD: float, St_BOD_effluent: float, Xt_MLSS_actual: float,
        Qw: float, num_pumps: int, TNt: float = None
    ) -> tuple[str, str]:
        """
        使用专业工艺计算方式分析好氧池运行情况。

        Args:
            V: 好氧池有效体积 (m³)
            Qy: 上一天处理水量 (m³/day)
            Sy_BOD: 上一天进水BOD浓度 (mg/L)
            Xy_MLSS: 上一天好氧池MLSS浓度 (mg/L)
            Qt: 今天当前处理水量 (m³/day) - 不用于计算
            St_BOD: 今天当前进水BOD浓度 (mg/L)
            St_BOD_effluent: 今天当前出水BOD浓度 (mg/L)
            Xt_MLSS_actual: 今天当前好氧池MLSS浓度 (mg/L)
            Qw: 单台排泥泵流量 (m³/hour)
            num_pumps: 计划用于排泥的泵数量
            TNt: (Optional) 今天当前出水总氮 (mg/L)

        Returns:
            tuple[str, str]: (分析过程, 操作建议和总结)
        """
        design_params = {'V': V, 'R_external': 0.7}
        yesterday_data = {'Qy': Qy, 'Sy_BOD': Sy_BOD, 'Xy_MLSS': Xy_MLSS}
        today_data = {'Qt': Qt, 'St_BOD': St_BOD, 'St_BOD_effluent': St_BOD_effluent, 'Xt_MLSS_actual': Xt_MLSS_actual, 'Qw': Qw, 'num_pumps': num_pumps, 'TNt': TNt}

        # --- 使用专业工艺计算方式 ---
        result = self.calculate_aerobic_tank_operation(**design_params, **yesterday_data, **today_data)

        # --- 分析过程 ---》》
        # analysis = f"--- 分析过程 (R=0.7) ---\n{result['analysis']}"
        # --- 创建参与计算的关键数据显示 ---
        data_summary = "=== 专业工艺计算参数 ===\n"
        data_summary += f"目标污泥负荷 (F/M): {result.get('F_M_target', 'N/A'):.3f} kgBOD/(kgMLSS·d)\n"
        data_summary += f"产泥系数 (Y): 1.25\n"
        data_summary += f"好氧池有效体积 (V): {V} m³\n\n"

        data_summary += "昨天的数据:\n"
        data_summary += f"- 处理水量 (Qy): {Qy} m³/day\n"
        data_summary += f"- 好氧池MLSS浓度 (Xy_MLSS): {Xy_MLSS} mg/L\n\n"

        data_summary += "今天的数据:\n"
        data_summary += f"- 进水BOD浓度 (St_BOD): {St_BOD} mg/L\n"
        data_summary += f"- 出水BOD浓度 (St_BOD_effluent): {St_BOD_effluent} mg/L\n"
        data_summary += f"- 当前好氧池MLSS浓度 (Xt_MLSS_actual): {Xt_MLSS_actual} mg/L\n"
        data_summary += f"- 单台排泥泵流量 (Qw): {Qw} m³/hour\n"
        data_summary += f"- 排泥泵数量: {num_pumps}\n\n"

        data_summary += "=== 计算结果 ===\n"
        data_summary += f"目标MLSS浓度: {result.get('X_target', 'N/A'):.2f} mg/L\n"
        data_summary += f"污泥增量: {result.get('Vincrease', 'N/A'):.2f} kg\n"
        data_summary += f"所需排泥干重: {result.get('Xp', 'N/A'):.2f} kg\n\n"

        analysis = f"{data_summary}--- 详细分析过程 ---\n{result['analysis']}"
        # --- 分析过程 ---《《
        
        # --- 操作建议和总结 ---
        recommendation = f"--- 专业工艺计算建议 ---\n{result['recommendation']}\n"
        if result['wasting_needed']:
            recommendation += f"(计算排泥时间: {result.get('wasting_time_hours', 'N/A'):.2f} 小时)\n"

        # --- 最终总结 ---
        summary = "\n===================================================\n"
        summary += "=== 总结 ===\n"
        summary += "===================================================\n"
        if result['wasting_needed']:
            summary += f"根据专业工艺计算方式的结果：\n"
            summary += f"- 污泥增量：{result.get('Vincrease', 'N/A'):.2f} kg\n"
            summary += f"- 所需排泥干重：{result.get('Xp', 'N/A'):.2f} kg\n"
            summary += f"- 计算排泥时间：{result.get('wasting_time_hours', 'N/A'):.2f} 小时\n"
            summary += f"\n建议根据上述专业计算结果进行排泥操作"
        else:
            summary += "根据专业工艺计算，当前条件下无需排泥。"
        summary += "\n==================================================="

        recommendation += summary

        return analysis, recommendation
    
    def analyze_collected_data(self, aerobic_data_by_camera, tank_volume, pump_flow_rate, num_pumps):
        """
        分析从摄像头收集到的数据并生成建议
        
        Args:
            aerobic_data_by_camera: 从AerobicDataCollector收集的数据
            tank_volume: 好氧池体积(m³)
            pump_flow_rate: 排泥泵流量(m³/hour)
            num_pumps: 排泥泵数量
            
        Returns:
            分析结果和建议的字典
        """
        results = {}
        
        for camera_id, camera_data in aerobic_data_by_camera.items():
            today_params = camera_data.get("today_parameters", {})
            yesterday_params = camera_data.get("yesterday_parameters", {})
            
            # 检查是否有足够的数据进行分析
            required_today = ["污水处理量", "BOD", "mlss", "出水BOD"]
            required_yesterday = ["Qy", "Sy_BOD", "Xy_MLSS", "S2_BOD"]
            
            has_required_data = True
            for param in required_today:
                if param not in today_params:
                    has_required_data = False
                    self.logger.warning(f"缺少今天的必要参数 {param} 用于相机 {camera_id}")
                    
            for param in required_yesterday:
                if param not in yesterday_params:
                    has_required_data = False
                    self.logger.warning(f"缺少昨天的必要参数 {param} 用于相机 {camera_id}")
            
            if not has_required_data:
                results[camera_id] = {
                    "status": "incomplete_data",
                    "message": "缺少进行分析所需的完整数据"
                }
                continue
                
            # 提取参数值
            try:
                Qt = float(today_params["污水处理量"].get("value", 0))
                St_BOD = float(today_params["BOD"].get("value", 0))
                St_BOD_effluent = float(today_params["出水BOD"].get("value", 0))
                Xt_MLSS_actual = float(today_params["mlss"].get("value", 0))

                Qy = float(yesterday_params["Qy"].get("value", 0))
                Sy_BOD = float(yesterday_params["Sy_BOD"].get("value", 0))
                Xy_MLSS = float(yesterday_params["Xy_MLSS"].get("value", 0))

                # 进行专业工艺分析
                analysis, recommendation = self.analyze_aerobic_tank_range(
                    V=tank_volume,
                    Qy=Qy,
                    Sy_BOD=Sy_BOD,
                    Xy_MLSS=Xy_MLSS,
                    Qt=Qt,
                    St_BOD=St_BOD,
                    St_BOD_effluent=St_BOD_effluent,
                    Xt_MLSS_actual=Xt_MLSS_actual,
                    Qw=pump_flow_rate,
                    num_pumps=num_pumps
                )
                
                # 直接调用专业工艺计算获取结果
                design_params = {'V': tank_volume, 'R_external': 0.7}
                yesterday_data = {'Qy': Qy, 'Sy_BOD': Sy_BOD, 'Xy_MLSS': Xy_MLSS}
                today_data = {'Qt': Qt, 'St_BOD': St_BOD, 'St_BOD_effluent': St_BOD_effluent, 'Xt_MLSS_actual': Xt_MLSS_actual, 'Qw': pump_flow_rate, 'num_pumps': num_pumps}

                # 直接获取计算结果
                calc_result = self.calculate_aerobic_tank_operation(**design_params, **yesterday_data, **today_data)
                
                # 提取排泥时间
                wasting_time_hours = calc_result.get('wasting_time_hours', None)
                wasting_needed = calc_result.get('wasting_needed', False)
                
                # 格式化X_target为两位小数
                x_target = calc_result.get('X_target')
                if x_target is not None:
                    x_target = round(x_target, 2)
                
                results[camera_id] = {
                    "status": "success",
                    "analysis": analysis,
                    "recommendation": recommendation,
                    "wasting_time_hours": wasting_time_hours,
                    "wasting_needed": wasting_needed,
                    "X_target": x_target,
                    "parameters": {
                        "today": {
                            "污水处理量": Qt,
                            "BOD": St_BOD,
                            "出水BOD": St_BOD_effluent,
                            "mlss": Xt_MLSS_actual
                        },
                        "yesterday": {
                            "Qy": Qy,
                            "Sy_BOD": Sy_BOD,
                            "Xy_MLSS": Xy_MLSS
                        }
                    }
                }
                
            except Exception as e:
                self.logger.error(f"分析相机 {camera_id} 的数据时发生错误: {e}", exc_info=True)
                results[camera_id] = {
                    "status": "error",
                    "message": f"分析过程中发生错误: {str(e)}"
                }
        
        return results

def publish_analysis_to_database(cameras_config, analysis_results):
    """
    将好氧池分析结果发布到数据库
    
    Args:
        cameras_config: 摄像头配置信息
        analysis_results: 好氧池分析结果
    """
    try:
        # 初始化日志
        log = logging.getLogger(__name__)
        
        # 初始化数据发布器
        data_publisher = DataPublisher(config.env)
        
        # 遍历所有分析结果
        for camera_id, result in analysis_results.items():
            # 检查相机数据有效性
            if result["status"] != "success":
                log.warning(f"摄像头 {camera_id} 分析失败，不发布到数据库")
                continue
            
            # 从analysis_results中获取关键参数，检查是否有6个必要参数
            params = result.get("parameters", {})
            today_params = params.get("today", {})
            yesterday_params = params.get("yesterday", {})
            
            # 检查必要参数是否存在
            required_params = [
                today_params.get("污水处理量"),
                today_params.get("BOD"),
                today_params.get("出水BOD"),
                today_params.get("mlss"),
                yesterday_params.get("Qy"),
                yesterday_params.get("Sy_BOD"),
                yesterday_params.get("Xy_MLSS")
            ]
            
            # 如果有任何一个参数不存在，则跳过此相机
            if None in required_params or 0 in required_params:
                log.warning(f"摄像头 {camera_id} 缺少必要参数，不发布到数据库")
                continue
                
            # 获取摄像头配置信息
            camera_config = next((cam for cam in cameras_config if cam['camera_id'] == camera_id), None)
            if not camera_config:
                log.warning(f"无法获取摄像头 {camera_id} 的配置信息，跳过")
                continue
                
            video_id = camera_config.get('video_id')
            if not video_id:
                log.warning(f"摄像头 {camera_id} 缺少video_id，跳过")
                continue
                
            # 准备LLM处理的输入字符串
            sludge_discharge_input = f"""
            以下是今天的运行情况:
            今天当前处理水量: {float(today_params.get('污水处理量', 0)):.2f}万m³/d
            今天当前进水BOD浓度: {float(today_params.get('BOD', 0)):.2f}mg/L
            今天当前出水BOD浓度: {float(today_params.get('出水BOD', 0)):.2f}mg/L
            今天当前好氧池MLSS浓度: {float(today_params.get('mlss', 0)):.2f}mg/L
            目标MLSS浓度: {float(result.get('X_target', 0)):.2f}mg/L
            污泥增量: {float(result.get('Vincrease', 0)):.2f}kg
            所需排泥干重: {float(result.get('Xp', 0)):.2f}kg

            以下是昨天的运行情况:
            上一天处理水量: {float(yesterday_params.get('Qy', 0)):.2f}万m³/d
            上一天进水BOD浓度: {float(yesterday_params.get('Sy_BOD', 0)):.2f}mg/L
            上一天好氧池MLSS浓度: {float(yesterday_params.get('Xy_MLSS', 0)):.2f}mg/L

            使用专业工艺计算方式（产泥系数1.25，目标F/M=0.111）计算出来今天的排泥时长为:
            """
            
            # 添加排泥时间信息
            wasting_needed = result.get("wasting_needed", False)
            wasting_time_hours = result.get("wasting_time_hours")
            
            if wasting_needed and wasting_time_hours is not None:
                sludge_discharge_input += f"{wasting_time_hours:.2f}小时"
            else:
                sludge_discharge_input += "0小时，当前MLSS浓度低于目标浓度，无需排泥"
            
            # 调用LLM处理函数获取analysis_detail和adjustment_suggestion
            log.info(f"正在调用LLM处理摄像头 {camera_id} 的好氧池数据")
            llm_result = llm_process_sludge_discharge(sludge_discharge_input)
            
            if llm_result:
                try:
                    # 尝试解析LLM返回的JSON字符串
                    logging.info(f"LLM返回的结果是: {llm_result}")
                    llm_result_json = loads(llm_result)
                    # ------模拟llm_result_json-----------
                    # llm_result_json = {
                    #     "analysis_detail": result["analysis"],
                    #     "adjustment_suggestion": result["recommendation"]
                    # }
                    analysis_detail = llm_result_json.get("analysis_detail")
                    adjustment_suggestion = llm_result_json.get("adjustment_suggestion")
                    log.info(f"成功解析LLM返回的JSON结果")
                except json.JSONDecodeError as e:
                    log.error(f"解析LLM返回的JSON结果失败: {e}，使用原始分析结果")
                    analysis_detail = result["analysis"]
                    adjustment_suggestion = result["recommendation"]
            else:
                log.warning(f"LLM处理摄像头 {camera_id} 的好氧池数据失败，使用原始分析结果")
                analysis_detail = result["analysis"]
                adjustment_suggestion = result["recommendation"]
            
            # 在分析和建议末尾添加备注
            备注信息 = "\n\n备注：当前推荐的排泥时长是根据单台设备计算所得。"
            analysis_detail = analysis_detail + 备注信息 if analysis_detail else ""
            adjustment_suggestion = adjustment_suggestion + 备注信息 if adjustment_suggestion else ""
            
            # 设置数据库字段值
            failure_reasons_type = []
            coverage_rate = 99
            coverage_level = 'HIGH'
            alarm_status = 'WARNING'
            is_abnormal = True
            do_value = 0
            mlss_value = 0
            alarmtype = '排泥建议,#448ef7'
            # 获取当前时间作为分析时间
            timestamp = datetime.datetime.now()
            frame_number = 0  # 默认值
            if base_dataset_path:
                # 构建完整的图片路径
                frame_path = os.path.join(base_dataset_path, "aerobic_tank_ops_image.png")
            else:
                frame_path = "save_data/datasets/2025/04/23/frame_4052_2025_04_23_16_21_33.jpg"   # 默认值
            
            # 准备发布到数据库的数据
            frame_data = (
                camera_id,                  # camera_id
                video_id,                   # video_id
                frame_number,               # frame_number
                timestamp,                  # timestamp
                frame_path,                 # frame_path
                coverage_rate,              # coverage_rate
                coverage_level,             # coverage_level
                alarm_status,               # alarm_status
                analysis_detail,            # analysis_detail
                is_abnormal,                # is_abnormal
                do_value,                   # do_value
                mlss_value,                 # mlss_value
                adjustment_suggestion,      # adjustment_suggestion
                failure_reasons_type,        # failure_reasons_type
                alarmtype
            )
            
            # 发布数据到数据库
            log.info(f"发布摄像头 {camera_id} 的好氧池分析结果到数据库")
            data_publisher._publish_to_database(frame_data)
            
    except Exception as e:
        log.error(f"发布好氧池分析结果到数据库时发生错误: {e}", exc_info=True)

def main():
    """
    主函数，用于收集数据、分析数据并发布到数据库，可作为定时任务调用
    """
    # 配置日志
    setup_logging()
    main_logger = logging.getLogger(__name__)

    try:
        # 获取摄像头配置
        cameras_config = get_cameras_config()

        # 收集数据
        collector = AerobicDataCollector(api_client)
        aerobic_data = collector.collect_data()

        # 如果没有收集到数据，记录并退出
        if not aerobic_data:
            main_logger.warning("没有收集到任何好氧池数据，无法进行分析")
        else:
            # 分析数据
            # 这些值应该从配置文件或其他地方获取
            tank_volume = 44437.5  # 好氧池体积
            pump_flow_rate = 75  # 排泥泵流量
            num_pumps = 1  # 排泥泵数量

            analyzer = AerobicTankAnalyzer()
            analysis_results = analyzer.analyze_collected_data(
                aerobic_data, tank_volume, pump_flow_rate, num_pumps
            )

            # 输出结果
            for camera_id, result in analysis_results.items():
                if result["status"] == "success":
                    main_logger.info(f"===== 相机 {camera_id} 分析结果 =====")
                    main_logger.info(result["analysis"])
                    main_logger.info(result["recommendation"])
                else:
                    main_logger.warning(f"相机 {camera_id} 分析失败: {result.get('message', '未知原因')}")

            # 将分析结果发布到数据库
            main_logger.info("将好氧池分析结果发布到数据库")
            publish_analysis_to_database(cameras_config, analysis_results)

    except Exception as e:
        main_logger.error(f"程序执行中发生错误: {e}", exc_info=True)

def test_with_mock_data():
    """
    使用模拟数据测试好氧池运行参数计算
    """
    # 配置日志
    setup_logging()
    test_logger = logging.getLogger(__name__)

    test_logger.info("=== 开始使用模拟数据测试好氧池运行参数计算 ===")

    # 模拟数据 - 基于实际污水处理厂的典型数值
    mock_data = {
        "camera_001": {
            "camera_id": "camera_001",
            "today_parameters": {
                "污水处理量": {"point_id": "p001", "value": 8.5, "monitor_time": "2025-01-15 14:30:00"},  # 万m³/d
                "BOD": {"point_id": "p002", "value": 180.0, "monitor_time": "2025-01-15 14:30:00"},  # mg/L
                "mlss": {"point_id": "p003", "value": 3200.0, "monitor_time": "2025-01-15 14:30:00"},  # mg/L
                "出水BOD": {"point_id": "p004", "value": 15.0, "monitor_time": "2025-01-15 14:30:00"}  # mg/L
            },
            "yesterday_parameters": {
                "Qy": {"point_id": "p001", "value": 8.2, "monitor_time": "2025-01-14 23:59:59"},  # 万m³/d
                "Sy_BOD": {"point_id": "p002", "value": 175.0, "monitor_time": "2025-01-14 23:59:59"},  # mg/L
                "Xy_MLSS": {"point_id": "p003", "value": 3100.0, "monitor_time": "2025-01-14 23:59:59"},  # mg/L
                "S2_BOD": {"point_id": "p004", "value": 12.0, "monitor_time": "2025-01-14 23:59:59"}  # mg/L
            }
        }
    }

    # 设计参数
    tank_volume = 44437.5  # 好氧池体积 (m³)
    pump_flow_rate = 75  # 排泥泵流量 (m³/hour)
    num_pumps = 1  # 排泥泵数量

    test_logger.info("=== 模拟数据参数 ===")
    test_logger.info(f"好氧池体积: {tank_volume} m³")
    test_logger.info(f"排泥泵流量: {pump_flow_rate} m³/hour")
    test_logger.info(f"排泥泵数量: {num_pumps}")

    for camera_id, camera_data in mock_data.items():
        test_logger.info(f"\n=== 相机 {camera_id} 的模拟数据 ===")
        test_logger.info("今天的参数:")
        for param, data in camera_data["today_parameters"].items():
            test_logger.info(f"  {param}: {data['value']}")

        test_logger.info("昨天的参数:")
        for param, data in camera_data["yesterday_parameters"].items():
            test_logger.info(f"  {param}: {data['value']}")

    # 创建分析器并分析数据
    analyzer = AerobicTankAnalyzer()
    analysis_results = analyzer.analyze_collected_data(
        mock_data, tank_volume, pump_flow_rate, num_pumps
    )

    # 输出分析结果
    test_logger.info("\n" + "="*80)
    test_logger.info("=== 分析结果 ===")
    test_logger.info("="*80)

    for camera_id, result in analysis_results.items():
        if result["status"] == "success":
            test_logger.info(f"\n===== 相机 {camera_id} 分析结果 =====")
            test_logger.info("\n--- 详细分析过程 ---")
            test_logger.info(result["analysis"])
            test_logger.info("\n--- 操作建议 ---")
            test_logger.info(result["recommendation"])

            # 输出关键计算结果
            test_logger.info(f"\n--- 关键计算结果 ---")
            test_logger.info(f"是否需要排泥: {'是' if result['wasting_needed'] else '否'}")
            test_logger.info(f"目标MLSS浓度: {result['X_target']:.2f} mg/L")
            if result['wasting_time_hours'] is not None:
                test_logger.info(f"建议排泥时间: {result['wasting_time_hours']:.2f} 小时")
        else:
            test_logger.error(f"相机 {camera_id} 分析失败: {result.get('message', '未知原因')}")

    test_logger.info("\n=== 模拟数据测试完成 ===")
    return analysis_results

# --- 示例：处理外回流比范围 (50%-150%) ---
if __name__ == "__main__":
    import sys

    # 检查命令行参数，如果有 --test 参数则运行测试模式
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        print("运行测试模式...")
        test_with_mock_data()
    else:
        print("运行正常模式...")
        main()