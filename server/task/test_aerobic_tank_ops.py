#!/usr/bin/env python3
"""
好氧池运行参数计算测试脚本

本脚本用于测试好氧池运行参数计算的各种场景，包括：
1. 正常运行场景
2. 需要排泥场景
3. 无需排泥场景
4. 边界条件测试

使用方法：
python test_aerobic_tank_ops.py
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from server.task.aerobic_tank_ops import AerobicTankAnalyzer
from server.utils.logger import setup_logging

def create_test_scenarios():
    """
    创建多种测试场景的模拟数据
    
    Returns:
        dict: 包含多个测试场景的字典
    """
    scenarios = {
        # "scenario_1_normal_operation": {
        #     "description": "正常运行场景 - 需要适量排泥",
        #     "data": {
        #         "camera_001": {
        #             "camera_id": "camera_001",
        #             "today_parameters": {
        #                 "污水处理量": {"point_id": "p001", "value": 8.5, "monitor_time": "2025-01-15 14:30:00"},
        #                 "BOD": {"point_id": "p002", "value": 180.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "mlss": {"point_id": "p003", "value": 3200.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "出水BOD": {"point_id": "p004", "value": 15.0, "monitor_time": "2025-01-15 14:30:00"}
        #             },
        #             "yesterday_parameters": {
        #                 "Qy": {"point_id": "p001", "value": 8.2, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Sy_BOD": {"point_id": "p002", "value": 175.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Xy_MLSS": {"point_id": "p003", "value": 3100.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "S2_BOD": {"point_id": "p004", "value": 12.0, "monitor_time": "2025-01-14 23:59:59"}
        #             }
        #         }
        #     }
        # },
        
        # "scenario_2_high_mlss": {
        #     "description": "高MLSS场景 - 需要大量排泥",
        #     "data": {
        #         "camera_002": {
        #             "camera_id": "camera_002",
        #             "today_parameters": {
        #                 "污水处理量": {"point_id": "p001", "value": 9.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "BOD": {"point_id": "p002", "value": 200.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "mlss": {"point_id": "p003", "value": 4500.0, "monitor_time": "2025-01-15 14:30:00"},  # 高MLSS
        #                 "出水BOD": {"point_id": "p004", "value": 18.0, "monitor_time": "2025-01-15 14:30:00"}
        #             },
        #             "yesterday_parameters": {
        #                 "Qy": {"point_id": "p001", "value": 8.8, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Sy_BOD": {"point_id": "p002", "value": 195.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Xy_MLSS": {"point_id": "p003", "value": 4200.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "S2_BOD": {"point_id": "p004", "value": 16.0, "monitor_time": "2025-01-14 23:59:59"}
        #             }
        #         }
        #     }
        # },
        
        # "scenario_3_low_mlss": {
        #     "description": "低MLSS场景 - 无需排泥",
        #     "data": {
        #         "camera_003": {
        #             "camera_id": "camera_003",
        #             "today_parameters": {
        #                 "污水处理量": {"point_id": "p001", "value": 7.5, "monitor_time": "2025-01-15 14:30:00"},
        #                 "BOD": {"point_id": "p002", "value": 160.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "mlss": {"point_id": "p003", "value": 2200.0, "monitor_time": "2025-01-15 14:30:00"},  # 低MLSS
        #                 "出水BOD": {"point_id": "p004", "value": 10.0, "monitor_time": "2025-01-15 14:30:00"}
        #             },
        #             "yesterday_parameters": {
        #                 "Qy": {"point_id": "p001", "value": 7.8, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Sy_BOD": {"point_id": "p002", "value": 165.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Xy_MLSS": {"point_id": "p003", "value": 2300.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "S2_BOD": {"point_id": "p004", "value": 8.0, "monitor_time": "2025-01-14 23:59:59"}
        #             }
        #         }
        #     }
        # },
        
        # "scenario_4_high_tn": {
        #     "description": "高总氮场景 - 需要考虑总氮影响的排泥",
        #     "data": {
        #         "camera_004": {
        #             "camera_id": "camera_004",
        #             "today_parameters": {
        #                 "污水处理量": {"point_id": "p001", "value": 8.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "BOD": {"point_id": "p002", "value": 190.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "mlss": {"point_id": "p003", "value": 3500.0, "monitor_time": "2025-01-15 14:30:00"},
        #                 "出水BOD": {"point_id": "p004", "value": 20.0, "monitor_time": "2025-01-15 14:30:00"}
        #             },
        #             "yesterday_parameters": {
        #                 "Qy": {"point_id": "p001", "value": 7.9, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Sy_BOD": {"point_id": "p002", "value": 185.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "Xy_MLSS": {"point_id": "p003", "value": 3300.0, "monitor_time": "2025-01-14 23:59:59"},
        #                 "S2_BOD": {"point_id": "p004", "value": 18.0, "monitor_time": "2025-01-14 23:59:59"}
        #             }
        #         }
        #     },
        #     "tn_value": 18.5  # 高总氮值
        # },

        "scenario_5_real_data_format": {
            "description": "真实数据格式测试 - 使用m³/d单位",
            "data": {
                "camera_005": {
                    "camera_id": "camera_005",
                    "today_parameters": {
                        "污水处理量": {"point_id": "p001", "value": 220000, "monitor_time": "2025-01-15 14:30:00"},  # m³/d
                        "BOD": {"point_id": "p002", "value": 82.3, "monitor_time": "2025-01-15 14:30:00"},  # mg/L
                        "mlss": {"point_id": "p003", "value": 3500.0, "monitor_time": "2025-01-15 14:30:00"},  # mg/L
                        "出水BOD": {"point_id": "p004", "value": 4.3, "monitor_time": "2025-01-15 14:30:00"}  # mg/L
                    },
                    "yesterday_parameters": {
                        "Qy": {"point_id": "p001", "value": 220000, "monitor_time": "2025-01-14 23:59:59"},  # m³/d
                        "Sy_BOD": {"point_id": "p002", "value": 80.0, "monitor_time": "2025-01-14 23:59:59"},  # mg/L
                        "Xy_MLSS": {"point_id": "p003", "value": 3400.0, "monitor_time": "2025-01-14 23:59:59"},  # mg/L
                        "S2_BOD": {"point_id": "p004", "value": 4.0, "monitor_time": "2025-01-14 23:59:59"}  # mg/L
                    }
                }
            },
            "additional_params": {
                "进水总氮": 36.0,  # mg/L
                "DO": 3.2  # mg/L
            }
        }
    }
    
    return scenarios

def run_test_scenario(scenario_name, scenario_data, tank_volume=44437.5, pump_flow_rate=75, num_pumps=1):
    """
    运行单个测试场景
    
    Args:
        scenario_name: 场景名称
        scenario_data: 场景数据
        tank_volume: 好氧池体积
        pump_flow_rate: 排泥泵流量
        num_pumps: 排泥泵数量
    """
    logger = logging.getLogger(__name__)
    
    logger.info(f"\n{'='*80}")
    logger.info(f"=== 测试场景: {scenario_name} ===")
    logger.info(f"=== 描述: {scenario_data.get('description', '无描述')} ===")
    logger.info(f"{'='*80}")
    
    # 创建分析器
    analyzer = AerobicTankAnalyzer()
    
    # 分析数据
    analysis_results = analyzer.analyze_collected_data(
        scenario_data['data'], tank_volume, pump_flow_rate, num_pumps
    )
    
    # 输出结果
    for camera_id, result in analysis_results.items():
        if result["status"] == "success":
            logger.info(f"\n--- 相机 {camera_id} 分析结果 ---")
            
            # 输出关键参数
            params = result.get("parameters", {})
            today_params = params.get("today", {})
            yesterday_params = params.get("yesterday", {})
            
            logger.info("输入参数:")
            logger.info(f"  今天 - 处理量: {today_params.get('污水处理量', 'N/A')} 万m³/d, "
                       f"进水BOD: {today_params.get('BOD', 'N/A')} mg/L, "
                       f"出水BOD: {today_params.get('出水BOD', 'N/A')} mg/L, "
                       f"MLSS: {today_params.get('mlss', 'N/A')} mg/L")
            logger.info(f"  昨天 - 处理量: {yesterday_params.get('Qy', 'N/A')} 万m³/d, "
                       f"进水BOD: {yesterday_params.get('Sy_BOD', 'N/A')} mg/L, "
                       f"MLSS: {yesterday_params.get('Xy_MLSS', 'N/A')} mg/L")
            
            # 输出计算结果
            logger.info(f"\n计算结果:")
            logger.info(f"  目标MLSS浓度: {result['X_target']:.2f} mg/L")
            logger.info(f"  是否需要排泥: {'是' if result['wasting_needed'] else '否'}")
            if result['wasting_time_hours'] is not None:
                logger.info(f"  建议排泥时间: {result['wasting_time_hours']:.2f} 小时")
            
            # 输出详细分析
            logger.info(f"\n详细分析过程:")
            logger.info(result["analysis"])
            
            logger.info(f"\n操作建议:")
            logger.info(result["recommendation"])
            
        else:
            logger.error(f"相机 {camera_id} 分析失败: {result.get('message', '未知原因')}")
    
    return analysis_results

def test_user_simple_data_format():
    """
    测试用户简单数据格式
    """
    logger = logging.getLogger(__name__)

    logger.info(f"\n{'='*80}")
    logger.info("=== 测试用户提供的实际数据 ===")
    logger.info(f"{'='*80}")

    # 用户提供的实际数据
    user_data = {
        # 设计参数
        'tank_volume': 44437.5,  # 好氧池有效体积（m³）

        # 昨天的运行数据
        'yesterday_flow': 220000,  # 处理水量（m³/d）

        # 今天的实际运行数据
        'today_mlss': 3500,  # MLSS浓度（mg/L）
        'today_influent_bod': 82.3,  # 进口BOD浓度（mg/L）
        'today_effluent_bod': 4.3,  # 出口BOD浓度（mg/L）
        'today_tn': 36,  # 进水总氮浓度（mg/L）
        'today_do': 3.2,  # DO（溶解氧）浓度（mg/L）

        # 其他关键数据
        'pump_flow_rate': 75,  # 单台排泥泵流量（m³/h）
        'pump_count': 1,  # 可用排泥泵数量（台）
    }

    logger.info("用户输入数据:")
    logger.info(f"  好氧池有效体积: {user_data['tank_volume']} m³")
    logger.info(f"  昨天处理水量: {user_data['yesterday_flow']} m³/d")
    logger.info(f"  今天MLSS浓度: {user_data['today_mlss']} mg/L")
    logger.info(f"  今天进水BOD: {user_data['today_influent_bod']} mg/L")
    logger.info(f"  今天出水BOD: {user_data['today_effluent_bod']} mg/L")
    logger.info(f"  今天总氮: {user_data['today_tn']} mg/L")
    logger.info(f"  今天DO: {user_data['today_do']} mg/L")
    logger.info(f"  排泥泵流量: {user_data['pump_flow_rate']} m³/h")
    logger.info(f"  排泥泵数量: {user_data['pump_count']} 台")

    # 创建分析器并分析
    analyzer = AerobicTankAnalyzer()

    try:
        results = analyzer.analyze_simple_data_format(user_data)

        # 输出结果
        for camera_id, result in results.items():
            if result["status"] == "success":
                logger.info(f"\n--- 分析结果 ---")
                logger.info(f"目标MLSS浓度: {result['X_target']:.2f} mg/L")
                logger.info(f"是否需要排泥: {'是' if result['wasting_needed'] else '否'}")
                if result['wasting_time_hours'] is not None and result['wasting_time_hours'] > 0:
                    logger.info(f"建议排泥时间: {result['wasting_time_hours']:.2f} 小时")

                logger.info(f"\n详细分析过程:")
                logger.info(result["analysis"])

                logger.info(f"\n操作建议:")
                logger.info(result["recommendation"])

            else:
                logger.error(f"分析失败: {result.get('message', '未知原因')}")

    except Exception as e:
        logger.error(f"测试用户简单数据格式时发生错误: {e}", exc_info=True)

def main():
    """
    主测试函数
    """
    # 配置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=== 好氧池运行参数计算测试开始 ===")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 设计参数
    tank_volume = 44437.5  # 好氧池体积 (m³)
    pump_flow_rate = 75  # 排泥泵流量 (m³/hour)
    num_pumps = 1  # 排泥泵数量

    logger.info(f"\n设计参数:")
    logger.info(f"  好氧池体积: {tank_volume} m³")
    logger.info(f"  排泥泵流量: {pump_flow_rate} m³/hour")
    logger.info(f"  排泥泵数量: {num_pumps}")

    # 首先测试用户简单数据格式
    test_user_simple_data_format()

    # 然后运行原有的测试场景
    logger.info(f"\n{'='*80}")
    logger.info("=== 运行原有测试场景 ===")
    logger.info(f"{'='*80}")

    # 获取测试场景
    scenarios = create_test_scenarios()

    # 运行所有测试场景
    all_results = {}
    for scenario_name, scenario_data in scenarios.items():
        try:
            results = run_test_scenario(
                scenario_name, scenario_data,
                tank_volume, pump_flow_rate, num_pumps
            )
            all_results[scenario_name] = results
        except Exception as e:
            logger.error(f"运行场景 {scenario_name} 时发生错误: {e}", exc_info=True)

    # 输出测试总结
    logger.info(f"\n{'='*80}")
    logger.info("=== 测试总结 ===")
    logger.info(f"{'='*80}")

    for scenario_name, results in all_results.items():
        logger.info(f"\n场景: {scenario_name}")
        for camera_id, result in results.items():
            if result["status"] == "success":
                wasting_status = "需要排泥" if result['wasting_needed'] else "无需排泥"
                wasting_time = f"{result['wasting_time_hours']:.2f}小时" if result['wasting_time_hours'] else "0小时"
                logger.info(f"  {camera_id}: {wasting_status}, 排泥时间: {wasting_time}")
            else:
                logger.info(f"  {camera_id}: 分析失败")

    logger.info(f"\n=== 好氧池运行参数计算测试完成 ===")

if __name__ == "__main__":
    main()
