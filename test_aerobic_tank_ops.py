#!/usr/bin/env python3
"""
测试修改后的好氧池运行参数计算模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.task.aerobic_tank_ops import AerobicTankAnalyzer


def test_professional_calculation():
    """测试专业工艺计算方式"""
    print("=== 测试专业工艺计算方式 ===")
    
    # 创建分析器
    analyzer = AerobicTankAnalyzer()
    
    # 测试数据
    test_params = {
        'V': 44437.5,           # 好氧池有效体积 (m³)
        'R_external': 0.7,      # 外回流比（保留但不用于计算）
        'Qy': 50000,            # 昨天处理水量 (m³/day)
        'Sy_BOD': 120,          # 昨天进水BOD浓度 (mg/L)
        'Xy_MLSS': 3500,        # 昨天好氧池MLSS浓度 (mg/L)
        'Qt': 52000,            # 今天处理水量（不用于计算）
        'St_BOD': 130,          # 今天进水BOD浓度 (mg/L)
        'St_BOD_effluent': 15,  # 今天出水BOD浓度 (mg/L)
        'Xt_MLSS_actual': 4200, # 今天实际MLSS浓度 (mg/L)
        'Qw': 75,               # 单台排泥泵流量 (m³/hour)
        'num_pumps': 1,         # 排泥泵数量
        'TNt': 12               # 出水总氮 (mg/L)
    }
    
    print("输入参数:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")
    print()
    
    # 执行计算
    try:
        result = analyzer.calculate_aerobic_tank_operation(**test_params)
        
        print("计算结果:")
        print(f"  目标污泥负荷 (F/M): {result.get('F_M_target', 'N/A')}")
        print(f"  目标MLSS浓度: {result.get('X_target', 'N/A'):.2f} mg/L")
        print(f"  污泥增量: {result.get('Vincrease', 'N/A'):.2f} kg")
        print(f"  需要排出的污泥总量: {result.get('delta_X_mass', 'N/A'):.2f} kg")
        print(f"  所需排泥干重: {result.get('Xp', 'N/A'):.2f} kg")
        print(f"  是否需要排泥: {result.get('wasting_needed', 'N/A')}")
        print(f"  回流污泥浓度: {result.get('MLSSv', 'N/A'):.2f} mg/L")
        print(f"  排泥时间: {result.get('wasting_time_hours', 'N/A'):.2f} 小时")
        print()
        
        print("详细分析过程:")
        print(result.get('analysis', ''))
        print()
        
        print("操作建议:")
        print(result.get('recommendation', ''))
        
    except Exception as e:
        print(f"计算过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_range_analysis():
    """测试范围分析功能"""
    print("\n" + "="*60)
    print("=== 测试范围分析功能 ===")
    
    analyzer = AerobicTankAnalyzer()
    
    try:
        analysis, recommendation = analyzer.analyze_aerobic_tank_range(
            V=44437.5,
            Qy=50000,
            Sy_BOD=120,
            Xy_MLSS=3500,
            Qt=52000,
            St_BOD=130,
            St_BOD_effluent=15,
            Xt_MLSS_actual=4200,
            Qw=75,
            num_pumps=1,
            TNt=12
        )
        
        print("分析过程:")
        print(analysis)
        print()
        print("建议:")
        print(recommendation)
        
    except Exception as e:
        print(f"范围分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_professional_calculation()
    test_range_analysis()
